# 🧪 Full-Stack Evaluator - Step-by-Step Development Guide

This guide provides a systematic approach to working on the full-stack evaluator project, prioritizing frontend development while gradually building backend understanding.

## 📋 Project Overview

**Current State Analysis:**
- ✅ Backend: Basic .NET 9 Web API with PostgreSQL and EF Core
- ✅ Frontend: React 18+ with Vite, Axios setup
- ✅ Database: Connection configured, migrations applied
- ⚠️ Missing: Full CRUD operations in frontend, error handling, validation

**Available API Endpoints:**
- `GET /tasks` - Retrieve all tasks
- `POST /tasks` - Create new task
- `PUT /tasks/{id}` - Update existing task
- `DELETE /tasks/{id}` - Delete task

**Data Models:**
- TaskItem: `{ Id, Title, IsDone, UserId, User }`
- User: `{ Id, Email, PasswordHash, Tasks[] }`

---

## 🎯 Phase 1: Initial Setup & Understanding (15-20 minutes)

### Step 1.1: Environment Setup
**What to do:**
```bash
# Frontend setup
cd frontend
npm install
# Check if .env file exists, create if needed
```

**What to look for:**
- Missing environment variables (VITE_API_BASE_URL)
- Package dependencies and versions
- Any build or configuration issues

**Expected commit:** `feat: setup frontend environment and dependencies`

### Step 1.2: Backend Verification
**What to do:**
```bash
# Backend verification
cd backend
dotnet restore
dotnet run
# Visit https://localhost:7xxx/swagger to see API docs
```

**What to look for:**
- API is running correctly
- Swagger documentation is accessible
- Database connection is working
- Available endpoints match expectations

**Expected commit:** `docs: verify backend API endpoints and swagger documentation`

---

## 🎯 Phase 2: Frontend Development (60-90 minutes)

### Step 2.1: API Configuration & Environment Setup
**What to do:**
- Create `.env` file in frontend directory
- Set up proper API base URL
- Test basic API connectivity

**Files to examine/create:**
- `frontend/.env`
- `frontend/src/api/axios.js`

**What to implement:**
```javascript
// .env
VITE_API_BASE_URL=https://localhost:7xxx
```

**Expected commit:** `feat: configure API base URL and environment variables`

### Step 2.2: Enhanced Task Display Component
**What to do:**
- Improve the existing Tasks.jsx component
- Add loading states and error handling
- Style the task list for better UX

**Files to modify:**
- `frontend/src/Tasks.jsx`
- `frontend/src/App.css` or create component-specific CSS

**What to implement:**
- Loading spinner/message
- Error state display
- Better task item styling
- Empty state handling

**Expected commit:** `feat: enhance task display with loading and error states`

### Step 2.3: Add Task Creation Form
**What to do:**
- Create a form component for adding new tasks
- Implement POST request to create tasks
- Add form validation

**Files to create/modify:**
- `frontend/src/components/TaskForm.jsx` (new)
- `frontend/src/Tasks.jsx` (integrate form)

**What to implement:**
- Controlled form inputs
- Form submission handling
- Optimistic UI updates
- Basic validation (required title)

**Expected commit:** `feat: implement task creation form with validation`

### Step 2.4: Task Status Toggle
**What to do:**
- Add ability to toggle task completion status
- Implement PUT request for task updates
- Add visual feedback for status changes

**Files to modify:**
- `frontend/src/Tasks.jsx`
- Add click handlers for task items

**What to implement:**
- Checkbox or button for toggling status
- PUT request to update task
- Optimistic UI updates
- Error handling for failed updates

**Expected commit:** `feat: add task status toggle functionality`

### Step 2.5: Task Deletion
**What to do:**
- Add delete functionality for tasks
- Implement confirmation dialog
- Handle DELETE requests

**Files to modify:**
- `frontend/src/Tasks.jsx`
- Possibly create a confirmation modal component

**What to implement:**
- Delete button for each task
- Confirmation before deletion
- DELETE request handling
- Remove from UI on success

**Expected commit:** `feat: implement task deletion with confirmation`

### Step 2.6: Error Handling & User Feedback
**What to do:**
- Implement comprehensive error handling
- Add user feedback for all operations
- Create reusable notification system

**Files to create/modify:**
- `frontend/src/components/Notification.jsx` (new)
- `frontend/src/hooks/useNotification.js` (new)
- Update all existing components

**What to implement:**
- Toast notifications or alert system
- Network error handling
- Success/failure feedback
- Retry mechanisms for failed requests

**Expected commit:** `feat: add comprehensive error handling and user notifications`

---

## 🎯 Phase 3: Backend Improvements (30-45 minutes)

### Step 3.1: API Validation & Error Handling
**What to do:**
- Add input validation to TasksController
- Improve error responses
- Add proper HTTP status codes

**Files to modify:**
- `backend/Controllers/TasksController.cs`

**What to implement:**
- Model validation attributes
- Try-catch blocks for database operations
- Consistent error response format
- Proper HTTP status codes (400, 404, 500)

**Expected commit:** `feat: add input validation and error handling to API`

### Step 3.2: CORS Configuration
**What to do:**
- Configure CORS to allow frontend requests
- Test cross-origin requests

**Files to modify:**
- `backend/Program.cs`

**What to implement:**
```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend",
        builder => builder
            .WithOrigins("http://localhost:5173") // Vite default port
            .AllowAnyMethod()
            .AllowAnyHeader());
});

app.UseCors("AllowFrontend");
```

**Expected commit:** `feat: configure CORS for frontend-backend communication`

### Step 3.3: Enhanced Task Model (Optional)
**What to do:**
- Add additional fields if time permits
- Consider adding CreatedAt, UpdatedAt timestamps
- Add Description field

**Files to modify:**
- `backend/Models/TaskItem.cs`
- Create new migration if changes are made

**What to implement:**
- Additional properties with proper attributes
- Database migration for schema changes
- Update controller to handle new fields

**Expected commit:** `feat: enhance task model with additional fields`

---

## 🎯 Phase 4: Testing & Validation (20-30 minutes)

### Step 4.1: Manual Testing Checklist
**What to test:**
- [ ] Frontend loads without errors
- [ ] Tasks display correctly
- [ ] Can create new tasks
- [ ] Can toggle task completion
- [ ] Can delete tasks
- [ ] Error handling works (disconnect backend, test network errors)
- [ ] Loading states appear appropriately
- [ ] Form validation works

**Expected commit:** `test: manual testing and bug fixes`

### Step 4.2: Code Review & Cleanup
**What to do:**
- Review all code for consistency
- Remove console.logs and debug code
- Add meaningful comments
- Ensure proper component structure

**Files to review:**
- All modified frontend files
- All modified backend files

**Expected commit:** `refactor: code cleanup and documentation`

---

## 🎯 Phase 5: Final Polish & Documentation (15-20 minutes)

### Step 5.1: Update Documentation
**What to do:**
- Update NOTES.md with implementation details
- Document any assumptions made
- List what's implemented vs. what's missing

**Files to modify:**
- `NOTES.md`

**What to document:**
- Features implemented
- Known issues or limitations
- How to test the application
- Future improvements

**Expected commit:** `docs: update project documentation and notes`

### Step 5.2: Final Testing & Deployment Prep
**What to do:**
- Final end-to-end testing
- Ensure both frontend and backend run correctly
- Test with fresh database

**Expected commit:** `feat: final testing and deployment preparation`

---

## 📝 Commit Strategy

**Commit Message Format:**
- `feat:` for new features
- `fix:` for bug fixes
- `refactor:` for code improvements
- `docs:` for documentation
- `test:` for testing

**Timing:**
- Commit after each major step (every 15-20 minutes)
- Don't wait until everything is perfect
- Show incremental progress and thought process

## 🚨 Common Pitfalls to Avoid

1. **Over-engineering:** Keep solutions simple and focused
2. **Perfect UI:** Focus on functionality over pixel-perfect design
3. **Complex state management:** Use local state unless Redux is specifically needed
4. **Extensive backend changes:** Work with existing structure when possible
5. **One large commit:** Commit frequently to show thought process

## 🎯 Success Criteria

- ✅ Frontend successfully communicates with backend
- ✅ All CRUD operations work correctly
- ✅ Proper error handling and user feedback
- ✅ Clean, readable code with good structure
- ✅ Regular commits showing development process
- ✅ Documentation of implementation choices

---

**Remember:** This is about demonstrating competency and thought process, not building a production-ready application. Focus on clean, working code that shows your development approach.
