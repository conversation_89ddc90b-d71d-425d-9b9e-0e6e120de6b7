import { useState } from 'react';
import api from '../api/axios';

function ApiTest() {
  const [testResult, setTestResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const testApiConnection = async () => {
    setIsLoading(true);
    setTestResult('Testing API connection...');
    
    try {
      console.log('Testing API connection to:', import.meta.env.VITE_API_BASE_URL);
      
      // Test GET /tasks endpoint
      const response = await api.get('/tasks');
      
      setTestResult(`✅ API Connection Successful!
      - Status: ${response.status}
      - Base URL: ${import.meta.env.VITE_API_BASE_URL}
      - Tasks found: ${response.data.length}
      - Response: ${JSON.stringify(response.data, null, 2)}`);
      
    } catch (error) {
      setTestResult(`❌ API Connection Failed!
      - Base URL: ${import.meta.env.VITE_API_BASE_URL}
      - Error: ${error.message}
      - Status: ${error.response?.status || 'No response'}
      - Details: ${error.response?.data ? JSON.stringify(error.response.data, null, 2) : 'No response data'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px', borderRadius: '8px' }}>
      <h3>🔧 API Connection Test</h3>
      <p><strong>API Base URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'Not configured'}</p>
      
      <button 
        onClick={testApiConnection} 
        disabled={isLoading}
        style={{ 
          padding: '10px 20px', 
          backgroundColor: '#007bff', 
          color: 'white', 
          border: 'none', 
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer'
        }}
      >
        {isLoading ? 'Testing...' : 'Test API Connection'}
      </button>
      
      {testResult && (
        <pre style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#f8f9fa', 
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          whiteSpace: 'pre-wrap',
          fontSize: '12px'
        }}>
          {testResult}
        </pre>
      )}
    </div>
  );
}

export default ApiTest;
