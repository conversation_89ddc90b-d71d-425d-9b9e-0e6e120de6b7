# Notes

- This is my first time working with .NET, so I’m still getting familiar with how it works.  
- For now, I’m following the instructions in the `backend/README.md` file step-by-step.  
- Noticed that upon opening the project, a `full-stack-evaluator.sln` file was generated.

## 1. feat: configure database connection in `appsettings.json`

- Updated `appsettings.json` to include the database connection string.  
- Ran `dotnet ef database update` on backend directory to apply the migrations.  

- I checked the API documentation at `http://localhost:5215/swagger/index.html`.  