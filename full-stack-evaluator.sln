Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "task-manager-api", "backend\task-manager-api.csproj", "{EF6E5B21-A278-CBBC-75B7-C013AED09D7D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EF6E5B21-A278-CBBC-75B7-C013AED09D7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF6E5B21-A278-CBBC-75B7-C013AED09D7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF6E5B21-A278-CBBC-75B7-C013AED09D7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF6E5B21-A278-CBBC-75B7-C013AED09D7D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {AF01946F-AB7B-47F1-9765-6AAE5D7DD5F7}
	EndGlobalSection
EndGlobal
